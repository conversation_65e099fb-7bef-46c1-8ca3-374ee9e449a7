# Armwrestling Power Arena: Theme & Styling Guide

This document outlines the styling approach, theme system, and configuration for the Armwrestling Power Arena application. It serves as the single source of truth for UI styling and theming.

## Core Technologies

-   **CSS Framework:** [Tailwind CSS](https://tailwindcss.com/) - Used for utility-first styling.
-   **UI Components:** [ShadCN UI](https://ui.shadcn.com/) - Provides a set of accessible and customizable base components.
-   **Theming:** [next-themes](https://github.com/pacocoursey/next-themes) - Manages theme switching (light/dark modes).

## Theme Overview

The application features a dual-theme system:

-   **Default Mode:** Dark.
-   **Light Mode:** Supported.
-   **Theme Switching:**
    -   Managed by `ThemeProvider` located in `src/components/providers/theme-provider.tsx`.
    -   The `ThemeToggle` component, found in `src/features/theme/ThemeToggle.tsx` (typically used in the Navbar), allows users to switch between themes.

## Color System

Colors are defined as CSS variables in `src/styles/globals.css` and are accessible via Tailwind utility classes. HSL (Hue, Saturation, Lightness) is the preferred format for defining color variables.

### Body Background

The main application body uses a dynamic gradient background:

-   `--gradient-start`: `hsl(275, 85%, 36%)` (#6a0dad)
-   `--gradient-middle`: `hsl(223, 64%, 33%)` (#1e3a8a)
-   `--gradient-end`: `hsl(175, 82%, 32%)` (#0d9488)

This is applied via a `linear-gradient` in the `body` styles in `src/styles/globals.css`.

### Primary Interactive Colors

-   **Primary Action (`--primary`):** `hsl(16, 100%, 50%)` (Burnt Orange - `#ff4500`)
    -   Used for main call-to-action buttons and critical interactive elements.
    -   Foreground: `hsl(var(--primary-foreground))` which is `hsl(0, 0%, 100%)` (White - `#FFFFFF`).
-   **Primary Action Hover (`--primary-hover`):** `hsl(16, 100%, 40%)` (Darker Burnt Orange - `#cc3700`)
    -   Used for the hover state of primary action elements.
-   **Secondary Action (`--secondary`):** `hsl(173, 57%, 39%)` (Teal - `#2A9D8F`)
    -   Used for alternative actions and supporting interactive elements.
    -   Foreground: `hsl(var(--secondary-foreground))` which is `hsl(0, 0%, 100%)` (White - `#FFFFFF`).

### Accent Color

-   **Accent (`--accent`):** `hsl(42, 22.10%, 69.80%)` 
    -   Used for highlights, important notifications, or badges.
    -   Foreground: `hsl(var(--accent-foreground))` which is `hsl(0, 0%, 0%)` (Black - `#000000`).

### UI Element Backgrounds (Panels, Popovers)

These colors adapt to the current theme (light/dark).

-   **Background (`--background`):**
    -   Light: `hsl(0, 0%, 97%)` (#F8F8F8)
    -   Dark: `hsl(223, 30%, 12%)` (#121212) - *Note: The body uses the gradient instead of this for the overall page background.*
-   **Card Background (`--card`):**
    -   Light: `hsl(0, 0%, 97%)` (#F8F8F8)
    -   Dark: `hsl(223, 30%, 12%)` (#121212)
-   **Popover Background (`--popover`):**
    -   Light: `hsl(0, 0%, 97%)` (#F8F8F8)
    -   Dark: `hsl(223, 30%, 12%)` (#121212)

### Text & Foreground Colors

These colors adapt to the current theme.

-   **Main Text (`--foreground`):**
    -   Light: `hsl(0, 0%, 26%)` (#424242)
    -   Dark: `hsl(0, 0%, 88%)` (#E0E0E0)
-   **Card Text (`--card-foreground`):**
    -   Light: `hsl(0, 0%, 26%)` (#424242)
    -   Dark: `hsl(0, 0%, 88%)` (#E0E0E0)
-   **Popover Text (`--popover-foreground`):**
    -   Light: `hsl(0, 0%, 26%)` (#424242)
    -   Dark: `hsl(0, 0%, 88%)` (#E0E0E0)
-   **Muted Text (`--muted-foreground`):**
    -   Light: `hsl(0, 0%, 46%)` (#757575)
    -   Dark: `hsl(0, 0%, 63%)` (#A0A0A0)

### Feedback Colors

-   **Destructive Action (`--destructive`):**
    -   Light: `hsl(0, 84%, 60%)`
    -   Dark: `hsl(0, 63%, 31%)`
    -   Foreground (`--destructive-foreground`): `hsl(0, 0%, 98%)` (for both modes)

### Border & Input Colors

-   **Border (`--border`):**
    -   Light: `hsl(0, 0%, 88%)` (#E0E0E0)
    -   Dark: `hsl(0, 0%, 15%)` (#262626)
-   **Input Background (`--input`):**
    -   Light: `hsl(0, 0%, 88%)` (#E0E0E0)
    -   Dark: `hsl(0, 0%, 15%)` (#262626)

### Ring Color (Focus Indicator)

-   **Ring (`--ring`):** `hsl(17, 45.20%, 69.20%)`

### Medal Colors

Used for achievements and rankings.

-   `--medal-gold`: `hsl(50, 100%, 50%)` (#FFD700)
-   `--medal-silver`: `hsl(0, 0%, 75%)` (#C0C0C0)
-   `--medal-bronze`: `hsl(30, 60%, 50%)` (#CD7F32)
-   `--medal-platinum`: `hsl(40, 7%, 90%)` (#E5E4E2)
-   `--medal-diamond`: `hsl(195, 100%, 86%)` (#B9F2FF)


## Typography

-   **Body Font:** Geist Sans
    -   Imported via `@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap")` in `src/styles/globals.css`.
    -   Applied globally via Tailwind's `font-sans` utility class, which is configured in `tailwind.config.ts`.
-   **Heading Font:** Oswald
    -   Imported using `next/font/google` in `src/app/layout.tsx`.
    -   The CSS variable `--font-heading` is assigned here and applied to the `<body>` tag.
    -   Tailwind's `font-heading` utility class (defined in `tailwind.config.ts` as `['var(--font-heading)', ...]`) makes this font easily accessible.

## Theme Configuration Files

-   **Global Styles & CSS Variables:** `src/styles/globals.css`
    -   This is the primary location for all CSS custom properties (variables) that define the theme palette for both light and dark modes.
    -   It also includes base styles and global imports like fonts.
-   **Tailwind Configuration:** `tailwind.config.ts`
    -   Extends Tailwind's default theme.
    -   Maps custom CSS variables to Tailwind utility classes (e.g., `colors: { primary: 'hsl(var(--primary))' }`).
    -   Configures font families (`font-sans`, `font-heading`), border radius, animations, etc.
-   **ShadCN UI Configuration:** `components.json`
    -   Defines settings for ShadCN UI, such as the alias for components and utils.

## UI Components

The application leverages [ShadCN UI](https://ui.shadcn.com/) for a base set of accessible and unstyled components, which are then styled using Tailwind CSS.

-   **Location:** Base ShadCN components are typically located in `src/components/ui/`.
-   **Customization:** Achieved by applying Tailwind utility classes directly or within custom wrapper components.

### Example Usage: Button

```tsx
import { Button } from '@/components/ui/button'; // Path might vary based on your ShadCN setup

export default function MyComponent() {
  return (
    <div>
      <Button>Default (Primary) Button</Button>
      <Button variant="secondary">Secondary Button</Button>
      <Button variant="destructive">Destructive Button</Button>
      <Button variant="outline" size="lg">
        Large Outline Button
      </Button>
    </div>
  );
}
```

### Example Usage: Card

```tsx
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card'; // Path might vary
import { Button } from '@/components/ui/button'; // Path might vary

export default function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card Description</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Card Content: Styled with Tailwind utilities.</p>
      </CardContent>
      <CardFooter>
        <Button>Action Button</Button>
      </CardFooter>
    </Card>
  );
}
```

## Best Practices

1.  **Use CSS Variables:** Always prefer using the defined CSS variables (e.g., `hsl(var(--primary))`) in `tailwind.config.ts` or custom CSS over hardcoding hex values.
2.  **Leverage Tailwind Utilities & Theme Colors:** Utilize Tailwind CSS classes for styling. For colors, always prefer theme-based utilities (e.g., `bg-background`, `text-primary`) over direct color specifications (e.g., `bg-gray-500`, `text-red-600`) to ensure consistency with the defined theme and support for light/dark modes. If a color is not in the theme, consider adding it as a CSS variable first.
3.  **`cn()` Utility:** Use the `cn()` utility function (typically from `src/lib/utils`) for conditionally combining Tailwind classes in components.
4.  **Component Reusability:** Build reusable components and style them with Tailwind, adhering to theme colors.
5.  **Theme Consistency:** Adhere to the defined color palette and typography to maintain visual consistency.
6.  **Test Both Modes:** Always verify UI appearance and readability in both light and dark themes.
7.  **Accessibility:** Keep accessibility in mind (color contrast, focus states) when applying styles. The `--ring` variable helps with focus visibility.

## Key File Locations Summary

-   **Theme & Styling Guide:** `docs/styling-guide.md`
-   **CSS Variables & Global Styles:** `src/styles/globals.css`
-   **Tailwind Configuration:** `tailwind.config.ts`
-   **ShadCN UI Configuration:** `components.json`
-   **Theme Provider Logic:** `src/components/providers/theme-provider.tsx`
-   **Theme Toggle Component:** `src/features/theme/ThemeToggle.tsx`
-   **Root Layout (Font Setup):** `src/app/layout.tsx`
-   **ShadCN UI Components:** `src/components/ui/` (or as per your alias in `components.json`) 