'use client'

import {
    signInAction,
    signUpAction
} from '@/app/auth/actions'
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import type { AuthErrorType } from '@/lib/validation/auth'
import { useActionState, useEffect, useState } from 'react'

interface AuthDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  initialMode?: 'signin' | 'signup'
}

export function AuthDialog({
  open,
  onOpenChange,
  initialMode = 'signin',
}: AuthDialogProps) {
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>(initialMode)
  const [showConfirmation, setShowConfirmation] = useState(false)

  const { signInWithSocial } = useAuth()

  // Replace complex state management with useActionState
  const [signInState, signInFormAction, isSignInPending] = useActionState(
    signInAction,
    null,
  )
  const [signUpState, signUpFormAction, isSignUpPending] = useActionState(
    signUpAction,
    null,
  )

  // Simplified effect - only for mode changes and confirmation reset
  useEffect(() => {
    setAuthMode(initialMode)
    setShowConfirmation(false)
  }, [initialMode])

  // Reset confirmation when dialog opens/closes
  useEffect(() => {
    if (open) {
      setShowConfirmation(false)
    }
  }, [open])

  // Handle successful signup with confirmation needed
  useEffect(() => {
    if (signUpState?.success && signUpState?.needsConfirmation) {
      setShowConfirmation(true)
    }
  }, [signUpState])

  // Handle successful auth - close dialog
  useEffect(() => {
    if (
      (signInState?.success && !signInState?.needsConfirmation) ||
      (signUpState?.success && !signUpState?.needsConfirmation)
    ) {
      onOpenChange(false)
    }
  }, [signInState, signUpState, onOpenChange])

  // Get current state and action based on mode
  const currentState = authMode === 'signin' ? signInState : signUpState
  const currentAction =
    authMode === 'signin' ? signInFormAction : signUpFormAction
  const isPending = authMode === 'signin' ? isSignInPending : isSignUpPending

  // Handler for social authentication (simplified)
  const handleSocialAuth = async (
    provider: 'google' | 'facebook' | 'apple',
  ) => {
    const result = await signInWithSocial(provider)
    // Social auth success/error handling is managed by the auth context
    // Successful social auth will redirect via the provider
  }

  // Handler to switch from signup confirmation back to signin
  const handleBackToSignIn = () => {
    setShowConfirmation(false)
    setAuthMode('signin')
  }

  // Helper function to get error-specific styling and suggestions
  const getErrorInfo = (errorType: AuthErrorType | undefined) => {
    switch (errorType) {
      case 'EMAIL_ALREADY_REGISTERED':
        return {
          showSignInSuggestion: true,
          className:
            'bg-primary/10 text-primary border-primary/20 dark:bg-primary/20 dark:text-primary dark:border-primary/30',
        }
      case 'INVALID_CREDENTIALS':
        return {
          showSignInSuggestion: false,
          className:
            'bg-destructive/10 text-destructive border-destructive/20 dark:bg-destructive/20 dark:text-destructive dark:border-destructive/30',
        }
      case 'EMAIL_NOT_CONFIRMED':
        return {
          showSignInSuggestion: false,
          className:
            'bg-accent/10 text-accent-foreground border-accent/20 dark:bg-accent/20 dark:text-accent dark:border-accent/30',
        }
      default:
        return {
          showSignInSuggestion: false,
          className:
            'bg-destructive/10 text-destructive border-destructive/20 dark:bg-destructive/20 dark:text-destructive dark:border-destructive/30',
        }
    }
  }

  const errorInfo = getErrorInfo(currentState?.errorType)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-semibold">
            {showConfirmation
              ? 'Check Your Email'
              : authMode === 'signin'
                ? 'Sign in'
                : 'Sign up'}
          </DialogTitle>
          <p className="mt-2 text-center text-muted-foreground">
            {showConfirmation
              ? 'We sent you a confirmation link'
              : authMode === 'signin'
                ? 'Welcome back! Please sign in to continue'
                : 'Create an account to get started'}
          </p>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {showConfirmation ? (
            // Email confirmation view
            <div className="space-y-4">
              <div className="p-4 rounded-lg bg-secondary/10 border border-secondary/20 dark:bg-secondary/20 dark:border-secondary/30">
                <div className="flex items-center space-x-2">
                  <div className="flex-shrink-0">
                    <svg
                      className="w-5 h-5 text-secondary dark:text-secondary"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                    >
                      <title>Success</title>
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="text-sm text-secondary dark:text-secondary">
                    <p className="font-medium">
                      {signUpState?.data?.message ||
                        'Please check your email and click the confirmation link to complete your registration.'}
                    </p>
                    <p className="mt-1">
                      Didn't receive the email? Check your spam folder or{' '}
                      <button
                        type="button"
                        onClick={handleBackToSignIn}
                        className="underline hover:no-underline font-medium"
                      >
                        try signing in
                      </button>{' '}
                      if you already confirmed your email.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={handleBackToSignIn}
                  className="flex-1 px-4 py-2 text-sm font-medium text-muted-foreground bg-muted border rounded-md hover:bg-muted/80 focus:outline-none focus:ring-2 focus:ring-ring dark:bg-muted dark:text-muted-foreground dark:hover:bg-muted/80"
                >
                  Back to Sign In
                </button>
                <button
                  type="button"
                  onClick={() => onOpenChange(false)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-primary-foreground bg-primary border border-transparent rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  Close
                </button>
              </div>
            </div>
          ) : (
            // Regular auth form view
            <div className="grid gap-2">
              {/* Error message with context-aware styling */}
              {currentState?.error && (
                <div
                  className={`p-3 text-sm rounded-md border ${errorInfo.className}`}
                >
                  <p>{currentState.error}</p>
                  {errorInfo.showSignInSuggestion && authMode === 'signup' && (
                    <button
                      type="button"
                      onClick={() => setAuthMode('signin')}
                      className="mt-2 text-sm underline hover:no-underline font-medium"
                    >
                      Sign in instead
                    </button>
                  )}
                </div>
              )}

              {/* Social Auth Buttons */}
              {/* <SocialAuthButtons
                onSocialAuth={handleSocialAuth}
                isLoading={isPending}
              />

              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-card px-2 text-muted-foreground">
                    or continue with
                  </span>
                </div>
              </div> */}

              {/* Modern form using server actions */}
              <form action={currentAction} className="space-y-4">
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium mb-1"
                  >
                    Email
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    disabled={isPending}
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50"
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium mb-1"
                  >
                    Password
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    disabled={isPending}
                    minLength={6}
                    className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50"
                    placeholder="Enter your password"
                  />
                </div>

                <button
                  type="submit"
                  disabled={isPending}
                  className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isPending
                    ? authMode === 'signin'
                      ? 'Signing in...'
                      : 'Signing up...'
                    : authMode === 'signin'
                      ? 'Sign In'
                      : 'Sign Up'}
                </button>
              </form>
            </div>
          )}
        </div>

        {!showConfirmation && (
          <div className="mt-2 text-center text-sm">
            {authMode === 'signin' ? (
              <p>
                Don't have an account?{' '}
                <button
                  type="button"
                  onClick={() => setAuthMode('signup')}
                  className="text-primary hover:underline dark:text-primary"
                  disabled={isPending}
                >
                  Sign up
                </button>
              </p>
            ) : (
              <p>
                Already have an account?{' '}
                <button
                  type="button"
                  onClick={() => setAuthMode('signin')}
                  className="text-primary hover:underline dark:text-primary"
                  disabled={isPending}
                >
                  Sign in
                </button>
              </p>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
