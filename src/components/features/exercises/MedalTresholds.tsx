import { Mars, Venus } from 'lucide-react'
import Image from 'next/image'

type MedalType = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond'

type MedalThresholdsProps = {
  thresholds?: Record<string, Record<string, number>>
}

const MedalThresholds = ({ thresholds }: MedalThresholdsProps) => {
  // Default thresholds if none provided
  const defaultThresholds = {
    men: {
      bronze: 20,
      silver: 30,
      gold: 40,
      platinum: 50,
      diamond: 60,
    },
    women: {
      bronze: 5,
      silver: 10,
      gold: 15,
      platinum: 20,
      diamond: 25,
    },
  }

  // Use provided thresholds or defaults
  const medalData = thresholds || defaultThresholds

  const medalTypes: Array<{ name: MedalType; image: string }> = [
    { name: 'bronze', image: '/images/medals/bronze.png' },
    { name: 'silver', image: '/images/medals/silver.png' },
    { name: 'gold', image: '/images/medals/gold.png' },
    { name: 'platinum', image: '/images/medals/platinum.png' },
    { name: 'diamond', image: '/images/medals/diamond.png' },
  ]

  const getMedalValue = (category: string, medalName: string): number => {
    const categoryData = medalData[category as keyof typeof medalData]
    if (categoryData && typeof categoryData === 'object') {
      return (categoryData as Record<string, number>)[medalName] || 0
    }
    return 0
  }

  return (
    <div className="w-full rounded-xl bg-card p-6 shadow-xl border overflow-hidden transition-shadow duration-300 hover:shadow-xl">
      <h2 className="mb-6 text-center text-xl font-bold text-card-foreground">
        Medal Thresholds
      </h2>

      <div className="space-y-8">
        {/* Men's thresholds */}
        <div>
          <h3 className="mb-4 flex items-center text-lg font-semibold text-card-foreground">
            <span className="mr-3 rounded-full bg-primary p-1.5">
              <Mars className="h-4 w-4 text-primary-foreground" />
            </span>
            Men
          </h3>

          <div className="grid grid-cols-5 gap-3 sm:gap-4">
            {medalTypes.map((medal) => (
              <div
                key={`men-${medal.name}`}
                className="flex flex-col items-center rounded-lg bg-muted p-3 transition-transform hover:scale-105 shadow-md"
              >
                <div className="mb-2 relative w-8 h-8 sm:w-10 sm:h-10">
                  <Image
                    src={medal.image}
                    alt={`${medal.name} medal`}
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="text-sm sm:text-base font-bold text-card-foreground">
                  {getMedalValue('men', medal.name) ||
                    getMedalValue('male', medal.name)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Women's thresholds */}
        <div>
          <h3 className="mb-4 flex items-center text-lg font-semibold text-card-foreground">
            <span className="mr-3 rounded-full bg-secondary p-1.5">
              <Venus className="h-4 w-4 text-secondary-foreground" />
            </span>
            Women
          </h3>

          <div className="grid grid-cols-5 gap-3 sm:gap-4">
            {medalTypes.map((medal) => (
              <div
                key={`women-${medal.name}`}
                className="flex flex-col items-center rounded-lg bg-muted p-3 transition-transform hover:scale-105 shadow-md"
              >
                <div className="mb-2 relative w-8 h-8 sm:w-10 sm:h-10">
                  <Image
                    src={medal.image}
                    alt={`${medal.name} medal`}
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="text-sm sm:text-base font-bold text-card-foreground">
                  {getMedalValue('women', medal.name) ||
                    getMedalValue('female', medal.name)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MedalThresholds
