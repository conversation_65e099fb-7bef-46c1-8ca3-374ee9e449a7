@import url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode theme variables - from styling guide */
    --background: hsl(0, 0%, 97%); /* #F8F8F8 */
    --foreground: hsl(0, 0%, 26%); /* #424242 */
    --card: hsl(0, 0%, 97%); /* #F8F8F8 */
    --card-foreground: hsl(0, 0%, 26%); /* #424242 */
    --popover: hsl(0, 0%, 97%); /* #F8F8F8 */
    --popover-foreground: hsl(0, 0%, 26%); /* #424242 */
    --primary: hsl(16, 100%, 50%); /* #ff4500 - Burnt Orange */
    --primary-foreground: hsl(0, 0%, 100%); /* #FFFFFF */
    --primary-hover: hsl(16, 100%, 40%); /* #cc3700 - Burnt Orange Hover */
    --secondary: hsl(173, 57%, 39%); /* #2A9D8F - Teal */
    --secondary-foreground: hsl(0, 0%, 100%); /* #FFFFFF */
    --muted: hsl(0, 0%, 94%); /* #F0F0F0 */
    --muted-foreground: hsl(0, 0%, 46%); /* #757575 */
    --accent: hsl(42, 22.10%, 69.80%); /* Accent from styling guide */
    --accent-foreground: hsl(0, 0%, 0%); /* #000000 */
    --destructive: hsl(0, 84%, 60%);
    --destructive-foreground: hsl(0, 0%, 98%);
    --border: hsl(0, 0%, 88%); /* #E0E0E0 */
    --input: hsl(0, 0%, 88%); /* #E0E0E0 */
    --ring: hsl(17, 45.20%, 69.20%); /* Ring from styling guide */
    --radius: 0.625rem;

    /* Gradient colors */
    --gradient-start: hsl(275, 85%, 36%); /* #6a0dad */
    --gradient-middle: hsl(223, 64%, 33%); /* #1e3a8a */
    --gradient-end: hsl(175, 82%, 32%); /* #0d9488 */

    /* Medal colors */
    --medal-gold: hsl(50, 100%, 50%); /* #FFD700 */
    --medal-silver: hsl(0, 0%, 75%); /* #C0C0C0 */
    --medal-bronze: hsl(30, 60%, 50%); /* #CD7F32 */
    --medal-platinum: hsl(40, 7%, 90%); /* #E5E4E2 */
    --medal-diamond: hsl(195, 100%, 86%); /* #B9F2FF */
  }

  .dark {
    /* Dark mode theme variables - from styling guide */
    --background: hsl(223, 30%, 12%); /* #121212 */
    --foreground: hsl(0, 0%, 88%); /* #E0E0E0 */
    --card: hsl(223, 30%, 12%); /* #121212 */
    --card-foreground: hsl(0, 0%, 88%); /* #E0E0E0 */
    --popover: hsl(223, 30%, 12%); /* #121212 */
    --popover-foreground: hsl(0, 0%, 88%); /* #E0E0E0 */
    --primary: hsl(16, 100%, 50%); /* #ff4500 - Burnt Orange */
    --primary-foreground: hsl(0, 0%, 100%); /* #FFFFFF */
    --primary-hover: hsl(16, 100%, 40%); /* #cc3700 - Burnt Orange Hover */
    --secondary: hsl(173, 57%, 39%); /* #2A9D8F - Teal */
    --secondary-foreground: hsl(0, 0%, 100%); /* #FFFFFF */
    --muted: hsl(0, 0%, 17%); /* #2C2C2C */
    --muted-foreground: hsl(0, 0%, 63%); /* #A0A0A0 */
    --accent: hsl(42, 22.10%, 69.80%); /* Accent from styling guide */
    --accent-foreground: hsl(0, 0%, 0%); /* #000000 */
    --destructive: hsl(0, 63%, 31%);
    --destructive-foreground: hsl(0, 0%, 98%);
    --border: hsl(0, 0%, 15%); /* #262626 */
    --input: hsl(0, 0%, 15%); /* #262626 */
    --ring: hsl(17, 45.20%, 69.20%); /* Ring from styling guide */

    /* Gradient colors (same for dark mode) */
    --gradient-start: hsl(275, 85%, 36%); /* #6a0dad */
    --gradient-middle: hsl(223, 64%, 33%); /* #1e3a8a */
    --gradient-end: hsl(175, 82%, 32%); /* #0d9488 */

    /* Medal colors (same for dark mode) */
    --medal-gold: hsl(50, 100%, 50%); /* #FFD700 */
    --medal-silver: hsl(0, 0%, 75%); /* #C0C0C0 */
    --medal-bronze: hsl(30, 60%, 50%); /* #CD7F32 */
    --medal-platinum: hsl(40, 7%, 90%); /* #E5E4E2 */
    --medal-diamond: hsl(195, 100%, 86%); /* #B9F2FF */
  }

  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply text-foreground;
    background: linear-gradient(
        135deg,
        hsl(var(--gradient-start)) 0%,
        hsl(var(--gradient-middle)) 50%,
        hsl(var(--gradient-end)) 100%
      )
      no-repeat center center fixed;
    background-size: cover;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
